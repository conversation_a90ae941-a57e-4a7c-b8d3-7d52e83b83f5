diff --git a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
index 5529cd649..202b35f6f 100644
--- a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
+++ b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
@@ -18,6 +18,7 @@ interface ShipTableRatesInterface
     const WEIGHT = 'weight';
     const SHIPPING_PRICE = 'shipping_price';
     const FREE_SHIPPING = 'free_shipping';
+    const MIN_ORDER_AMOUNT = 'min_order_amount';
 
     /**
      * Get shiptablerates_id
@@ -175,5 +176,18 @@ interface ShipTableRatesInterface
      */
     public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;
 
+    /**
+     * Get minimum order amount
+     * @return float|null
+     */
+    public function getMinOrderAmount(): ?float;
+
+    /**
+     * Set minimum order amount
+     * @param float $minOrderAmount
+     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface;
+
 }
 
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
index 07c62a935..c528c4167 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
@@ -5,7 +5,8 @@ namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
-     * Seller Category Edit action.
+     * Seller Shipping Rate Edit action.
+     * Handles both regular shipping methods and free shipping thresholds.
      *
      * @return \Magento\Framework\Controller\Result\RedirectFactory
      */
@@ -18,24 +19,31 @@ class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
             );
         }
 
+        $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+
         $resultPage = $this->_resultPageFactory->create();
         if ($this->_marketplaceHelper->getIsSeparatePanel()) {
-            $resultPage->addHandle('mpsellership_layout2_rate_edit');
+            if ($isThreshold) {
+                $resultPage->addHandle('mpsellership_layout2_threshold_edit');
+            } else {
+                $resultPage->addHandle('mpsellership_layout2_rate_edit');
+            }
         }
 
         if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
             $sellerShiprate = $this->getSellerShiprate();
             if (empty($sellerShiprate->getShiptableratesId())) {
-                $this->messageManager->addError("Shipping method does not exist");
+                $errorMessage = $isThreshold ? "Free shipping threshold does not exist" : "Shipping method does not exist";
+                $this->messageManager->addError($errorMessage);
                 return $this->resultRedirectFactory->create()->setPath(
                     'coditron_customshippingrate/shiptablerates/manage',
                     ['_secure' => $this->getRequest()->isSecure()]
                 );
             }
 
-            $title = $sellerShiprate->getCourierName();
+            $title = $isThreshold ? "Edit Free Shipping Threshold" : $sellerShiprate->getCourierName();
         } else {
-            $title = "New Shipping Method";
+            $title = $isThreshold ? "New Free Shipping Threshold" : "New Shipping Method";
         }
 
         $resultPage->getConfig()->getTitle()->set(__($title));
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
index e3f223dd4..e9e6685ee 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
@@ -10,7 +10,7 @@
  */
 namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 
-class Manage extends \Webkul\MpSellerCategory\Controller\AbstractCategory
+class Manage extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
      * Execute Method
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/NewAction.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/NewAction.php
index f56e1e836..3276167a9 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/NewAction.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/NewAction.php
@@ -5,7 +5,8 @@ namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 class NewAction extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
-     * Create New Seller Category action
+     * Create New Seller Shipping Rate action
+     * Forwards to edit action for both regular shipping methods and thresholds
      */
     public function execute()
     {
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
index a848ecfba..8a91ff800 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
@@ -13,7 +13,8 @@ namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
-     * Seller Category Save action
+     * Seller Shipping Rate Save action
+     * Handles both regular shipping methods and free shipping thresholds
      *
      * @return \Magento\Framework\Controller\Result\RedirectFactory
      */
@@ -34,10 +35,21 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
                 $result = $this->_helper->validateData($postData);
                 if ($result['error']) {
                     $this->messageManager->addError(__($result['msg']));
-                    return $this->resultRedirectFactory->create()->setPath(
-                        'coditron_customshippingrate/shiptablerates/manage',
-                        ['_secure' => $this->getRequest()->isSecure()]
-                    );
+
+                    $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                    $redirectPath = 'coditron_customshippingrate/shiptablerates/new';
+                    $redirectParams = ['_secure' => $this->getRequest()->isSecure()];
+
+                    if ($isThreshold) {
+                        $redirectParams['is_threshold'] = 1;
+                    }
+
+                    if (!empty($postData['id'])) {
+                        $redirectPath = 'coditron_customshippingrate/shiptablerates/edit';
+                        $redirectParams['shiptablerates_id'] = $postData['id'];
+                    }
+
+                    return $this->resultRedirectFactory->create()->setPath($redirectPath, $redirectParams);
                 }
 
                 $sellerShiprate = $this->getSellerShiprate();
@@ -51,11 +63,20 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 
                 $sellerShiprate->save();
                 $id = $sellerShiprate->getShiptableratesId();
-                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
+
+                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                $successMessage = $isThreshold ? "Free Shipping Threshold saved successfully." : "Shipping Rate saved successfully.";
+                $this->messageManager->addSuccess(__($successMessage));
                 $this->_helper->clearCache();
+
+                $params = ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()];
+                if ($isThreshold) {
+                    $params['is_threshold'] = 1;
+                }
+
                 return $this->resultRedirectFactory->create()->setPath(
                     'coditron_customshippingrate/shiptablerates/edit',
-                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    $params
                 );
             } catch (\Exception $e) {
                 $this->messageManager->addError($e->getMessage());
diff --git a/app/code/Coditron/CustomShippingRate/Helper/Data.php b/app/code/Coditron/CustomShippingRate/Helper/Data.php
index 117b988ca..1bce612f3 100755
--- a/app/code/Coditron/CustomShippingRate/Helper/Data.php
+++ b/app/code/Coditron/CustomShippingRate/Helper/Data.php
@@ -270,6 +270,77 @@ class Data extends AbstractHelper
         return false;
     }
 
+    /**
+     * Check if free shipping threshold already exists for any of the selected countries
+     *
+     * @param array $countries
+     * @param int $sellerId
+     * @param mixed $rateId
+     * @return array Array of countries that already have thresholds
+     */
+    public function getExistingThresholdCountries(array $countries, int $sellerId = 0, $rateId = 0): array
+    {
+        $rateId = (int) $rateId;
+
+        if (!$rateId) {
+            $rateId = (int) $this->_request->getParam("id");
+        }
+
+        if (!$sellerId) {
+            $sellerId = $this->getSellerId();
+        }
+
+        $existingCountries = [];
+
+        foreach ($countries as $country) {
+            $country = trim($country);
+            if (empty($country)) {
+                continue;
+            }
+
+            $collection = $this->_mpSellerShipRateCollectionFactory->create();
+            $collection->addFieldToFilter("seller_id", ["eq" => $sellerId])
+                      ->addFieldToFilter("free_shipping", ["eq" => 1])
+                      ->addFieldToFilter("min_order_amount", ["gt" => 0])
+                      ->addFieldToFilter("countries", ["like" => "%{$country}%"]);
+
+            if ($rateId) {
+                $collection->addFieldToFilter("shiptablerates_id", ["neq" => $rateId]);
+            }
+
+            if ($collection->getSize() > 0) {
+                $existingCountries[] = $country;
+            }
+        }
+
+        return $existingCountries;
+    }
+
+    /**
+     * Get free shipping threshold for the given country that is met by the subtotal
+     *
+     * @param string $country
+     * @param float $subtotal
+     * @return float|null
+     */
+    public function getFreeShippingThresholdForCountry($country, $subtotal)
+    {
+        if (!$country || $subtotal <= 0) {
+            return null;
+        }
+
+        $collection = $this->_mpSellerShipRateCollectionFactory->create();
+        $collection->addFieldToFilter('free_shipping', 1)
+                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
+                   ->addFieldToFilter('countries', ['like' => "%{$country}%"])
+                   ->setOrder('min_order_amount', 'ASC');
+
+        $threshold = $collection->getFirstItem();
+
+        return $threshold->getId() ? $threshold->getMinOrderAmount() : null;
+    }
+
     /**
      * Validate Category Data
      *
@@ -312,6 +383,24 @@ class Data extends AbstractHelper
             return $result;
         }
 
+        $isThreshold = isset($details['is_threshold']) && $details['is_threshold'];
+        $isFreeShipping = isset($details['free_shipping']) && $details['free_shipping'];
+        $hasMinAmount = isset($details['min_order_amount']) && $details['min_order_amount'] > 0;
+
+        if ($isThreshold && $isFreeShipping && $hasMinAmount) {
+            $countries = is_array($details['countries']) ? $details['countries'] : explode(',', $details['countries']);
+            $sellerId = (int) ($details['seller_id'] ?? 0);
+            $rateId = (int) ($details['id'] ?? 0);
+
+            $existingCountries = $this->getExistingThresholdCountries($countries, $sellerId, $rateId);
+
+            if (!empty($existingCountries)) {
+                $result["error"] = true;
+                $countryList = implode(', ', $existingCountries);
+                $result["msg"] = "Free shipping threshold already exists for the following countries: {$countryList}. You can only edit existing thresholds.";
+                return $result;
+            }
+        }
 
         return $result;
     }
diff --git a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
index 5369590c3..7c24e25d2 100644
--- a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
+++ b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
@@ -215,6 +215,22 @@ class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
         return $this->setData(self::FREE_SHIPPING, $freeShipping);
     }
 
+    /**
+     * @inheritDoc
+     */
+    public function getMinOrderAmount(): ?float
+    {
+        return (float)$this->getData(self::MIN_ORDER_AMOUNT);
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
+    {
+        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
+    }
+
     /**
      * Override to Converts countries array to string
      * @param $key
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Model/Quote/AddressPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Model/Quote/AddressPlugin.php
index a863b5b1e..a5b1f7146 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Model/Quote/AddressPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Model/Quote/AddressPlugin.php
@@ -4,9 +4,23 @@ declare(strict_types=1);
 namespace Coditron\CustomShippingRate\Plugin\Model\Quote;
 
 use Magento\Quote\Api\Data\AddressInterface;
+use Coditron\CustomShippingRate\Helper\Data as HelperData;
 
 class AddressPlugin
 {
+    /**
+     * @var HelperData
+     */
+    protected $helper;
+
+    /**
+     * @param HelperData $helper
+     */
+    public function __construct(
+        HelperData $helper
+    ) {
+        $this->helper = $helper;
+    }
 
     /**
      * @param AddressInterface $subject
@@ -37,7 +51,12 @@ class AddressPlugin
                 if ($rate->getCode() == $subject->getShippingMethod()) {
                     $rate->setPrice($price);
                     $rate->setCost($price);
-                    $rate->setMethodTitle($description);
+
+                    if ($rate->getCarrier() === 'freeshipping') {
+                        $this->ensureFreeShippingThresholdSubtitle($rate, $subject);
+                    } else {
+                        $rate->setMethodTitle($description);
+                    }
                     break;
                 }
             }
@@ -45,4 +64,37 @@ class AddressPlugin
 
         return $return;
     }
+
+    /**
+     * Ensure free shipping threshold subtitle is always present
+     *
+     * @param \Magento\Quote\Model\Quote\Address\RateResult\Method $rate
+     * @param AddressInterface $address
+     * @return void
+     */
+    protected function ensureFreeShippingThresholdSubtitle($rate, $address)
+    {
+        $currentTitle = $rate->getMethodTitle();
+
+        if (strpos($currentTitle, 'For Orders Over') !== false) {
+            return;
+        }
+
+        $quote = $address->getQuote();
+        if (!$quote) {
+            $rate->setMethodTitle('Free Shipping');
+            return;
+        }
+
+        $country = $address->getCountryId();
+        $subtotal = $quote->getBaseSubtotalWithDiscount();
+
+        $threshold = $this->helper->getFreeShippingThresholdForCountry($country, $subtotal);
+        if ($threshold !== null) {
+            $newTitle = sprintf('Free (For Orders Over $%.2f)', $threshold);
+            $rate->setMethodTitle($newTitle);
+        } else {
+            $rate->setMethodTitle('Free Shipping');
+        }
+    }
 }
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
index 4043e1b79..f0c8b4673 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
@@ -16,6 +16,7 @@ use Magento\Quote\Model\Quote\Address\Total\Shipping;
 use Coditron\CustomShippingRate\Helper\Data;
 use Coditron\CustomShippingRate\Model\Carrier;
 
+
 class ShippingPlugin
 {
     /**
@@ -54,7 +55,7 @@ class ShippingPlugin
 
         if (!$this->customShippingRateHelper->isEnabled($storeId)
             || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
-            || strpos((string) $method, Carrier::CODE) === false
+            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping') === false)
         ) {
             return $proceed($quote, $shippingAssignment, $total);
         }
@@ -66,6 +67,11 @@ class ShippingPlugin
             $shipping->setMethod($customShippingOption['code']);
             $address->setShippingMethod($customShippingOption['code']);
             $this->updateCustomRate($address, $customShippingOption);
+
+            if (strpos($method, 'freeshipping') === 0) {
+                $total->setShippingAmount(0);
+                $total->setBaseShippingAmount(0);
+            }
         }
 
         return $proceed($quote, $shippingAssignment, $total);
@@ -108,11 +114,20 @@ class ShippingPlugin
                 $rate = $selectedRate->getPrice();
             }
 
-            $jsonToArray = [
-                'code' => $json,
-                'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
-                'rate' => $rate
-            ];
+            if (strpos($json, 'freeshipping') === 0) {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'free_shipping',
+                    'rate' => 0,
+                    'description' => 'Free Shipping'
+                ];
+            } else {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
+                    'rate' => $rate
+                ];
+            }
 
             return $this->formatShippingArray($jsonToArray);
         }
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php
new file mode 100644
index *********..9ae21cd58
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php
@@ -0,0 +1,136 @@
+<?php
+/**
+ * Copyright © Coditron Technologies All rights reserved.
+ * See COPYING.txt for license details.
+ * http://www.coditron.com | <EMAIL>
+ */
+
+namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;
+
+use Magento\OfflineShipping\Model\Carrier\Freeshipping;
+use Magento\Quote\Model\Quote\Address\RateRequest;
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
+
+/**
+ * Plugin to conditionally enable core freeshipping when thresholds exist and are met
+ */
+class FreeShippingPlugin
+{
+    /**
+     * @var ShipTableRatesCollectionFactory
+     */
+    protected $shipTableRatesCollectionFactory;
+
+    /**
+     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
+     */
+    public function __construct(
+        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
+    ) {
+        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
+    }
+
+    /**
+     * Enable core freeshipping only when thresholds exist and are met for the country
+     *
+     * @param Freeshipping $subject
+     * @param callable $proceed
+     * @param RateRequest $request
+     * @return \Magento\Shipping\Model\Rate\Result|bool
+     */
+    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
+    {
+        $country = $request->getDestCountryId();
+        $subtotal = $request->getBaseSubtotalInclTax();
+
+        $metThresholds = $this->getMetThresholdsForCountry($country, $subtotal);
+
+        if (empty($metThresholds)) {
+            return false;
+        }
+
+        $this->overrideFreeShippingConfig($subject, $metThresholds);
+
+        $result = $proceed($request);
+
+        if ($result && $result->getAllRates()) {
+            $this->addThresholdSubtitle($result, $metThresholds);
+        }
+
+        return $result;
+    }
+
+    /**
+     * Get free shipping thresholds for the given country that are met
+     *
+     * @param string $country
+     * @param float $subtotal
+     * @return array
+     */
+    protected function getMetThresholdsForCountry($country, $subtotal)
+    {
+        if (!$country || $subtotal <= 0) {
+            return [];
+        }
+
+        $collection = $this->shipTableRatesCollectionFactory->create();
+        $collection->addFieldToFilter('free_shipping', 1)
+                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
+                   ->addFieldToFilter('countries', ['like' => "%{$country}%"]);
+
+        return $collection->getItems();
+    }
+
+    /**
+     * Override free shipping configuration to enable it
+     *
+     * @param Freeshipping $subject
+     * @param array $metThresholds
+     * @return void
+     */
+    protected function overrideFreeShippingConfig(Freeshipping $subject, $metThresholds)
+    {
+        $lowestThreshold = min(array_map(function($threshold) {
+            return $threshold->getMinOrderAmount();
+        }, $metThresholds));
+
+        $reflection = new \ReflectionClass($subject);
+        if ($reflection->hasProperty('_configData')) {
+            $configProperty = $reflection->getProperty('_configData');
+            $configProperty->setAccessible(true);
+            $configData = $configProperty->getValue($subject) ?: [];
+
+            $configData['free_shipping_subtotal'] = $lowestThreshold;
+            $configProperty->setValue($subject, $configData);
+        }
+    }
+
+    /**
+     * Add threshold subtitle to free shipping method
+     *
+     * @param \Magento\Shipping\Model\Rate\Result $result
+     * @param array $metThresholds
+     * @return void
+     */
+    protected function addThresholdSubtitle($result, $metThresholds)
+    {
+        if (empty($metThresholds)) {
+            return;
+        }
+
+        $lowestThreshold = min(array_map(function($threshold) {
+            return $threshold->getMinOrderAmount();
+        }, $metThresholds));
+
+        foreach ($result->getAllRates() as $rate) {
+            $currentTitle = $rate->getMethodTitle();
+            if ($rate->getCarrier() === 'freeshipping') {
+                if (strpos($currentTitle, 'For Orders Over') === false) {
+                    $currentTitle = sprintf('Free (For Orders Over $%.2f)', $lowestThreshold);
+                    $rate->setMethodTitle($currentTitle);
+                }
+            }
+        }
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php b/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php
new file mode 100644
index *********..2a0a0b7c5
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php
@@ -0,0 +1,48 @@
+<?php
+/**
+ * Actions column for Free Shipping Threshold grid
+ */
+declare(strict_types=1);
+
+namespace Coditron\CustomShippingRate\Ui\Component\Listing\Column;
+
+class ThresholdActions extends ShipTableRatesActions
+{
+
+    /**
+     * Prepare Data Source
+     *
+     * @param array $dataSource
+     * @return array
+     */
+    public function prepareDataSource(array $dataSource)
+    {
+        if (isset($dataSource['data']['items'])) {
+            foreach ($dataSource['data']['items'] as &$item) {
+                $name = $this->getData('name');
+                if (isset($item['shiptablerates_id'])) {
+                    $item[$name]['edit'] = [
+                        'href' => $this->urlBuilder->getUrl(
+                            'coditron_customshippingrate/shiptablerates/edit',
+                            ['shiptablerates_id' => $item['shiptablerates_id'], 'is_threshold' => 1]
+                        ),
+                        'label' => __('Edit')
+                    ];
+                    $item[$name]['delete'] = [
+                        'href' => $this->urlBuilder->getUrl(
+                            static::URL_PATH_DELETE,
+                            ['shiptablerates_id' => $item['shiptablerates_id']]
+                        ),
+                        'label' => __('Delete'),
+                        'confirm' => [
+                            'title' => __('Delete Threshold'),
+                            'message' => __('Are you sure you want to delete this free shipping threshold?')
+                        ]
+                    ];
+                }
+            }
+        }
+
+        return $dataSource;
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
new file mode 100644
index *********..ac99d3d07
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
@@ -0,0 +1,97 @@
+<?php
+/**
+ * Data provider for Free Shipping Threshold listing
+ */
+declare(strict_types=1);
+
+namespace Coditron\CustomShippingRate\Ui\DataProvider;
+
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
+use Magento\Framework\Api\Filter;
+use Magento\Ui\DataProvider\AbstractDataProvider;
+use Coditron\CustomShippingRate\Helper\Data as CustomShippingHelper;
+
+class ThresholdListDataProvider extends AbstractDataProvider
+{
+    /**
+     * @var CustomShippingHelper
+     */
+    protected $helper;
+
+    /**
+     * @var array
+     */
+    protected $loadedData;
+
+    /**
+     * @param string $name
+     * @param string $primaryFieldName
+     * @param string $requestFieldName
+     * @param CollectionFactory $collectionFactory
+     * @param CustomShippingHelper $helper
+     * @param array $meta
+     * @param array $data
+     */
+    public function __construct(
+        $name,
+        $primaryFieldName,
+        $requestFieldName,
+        CollectionFactory $collectionFactory,
+        CustomShippingHelper $helper,
+        array $meta = [],
+        array $data = []
+    ) {
+        $this->collection = $collectionFactory->create();
+        $this->helper = $helper;
+        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
+    }
+
+    /**
+     * Get data
+     *
+     * @return array
+     */
+    public function getData()
+    {
+        if (isset($this->loadedData)) {
+            return $this->loadedData;
+        }
+
+        $sellerId = $this->helper->getSellerId();
+
+        // Filter collection to show only records with min_order_amount > 0 for current seller
+        $this->collection->addFieldToFilter('seller_id', $sellerId)
+                        ->addFieldToFilter('min_order_amount', ['gt' => 0]);
+
+        $this->loadedData = $this->collection->toArray();
+        return $this->loadedData;
+    }
+
+    /**
+     * Add filter
+     *
+     * @param Filter $filter
+     * @return void
+     */
+    public function addFilter(Filter $filter)
+    {
+        if ($filter->getField() !== 'fulltext') {
+            $this->collection->addFieldToFilter(
+                $filter->getField(),
+                [$filter->getConditionType() => $filter->getValue()]
+            );
+        } else {
+            $value = trim($filter->getValue());
+            $this->collection->addFieldToFilter(
+                [
+                    ['attribute' => 'countries'],
+                    ['attribute' => 'min_order_amount']
+                ],
+                [
+                    ['like' => "%{$value}%"],
+                    ['like' => "%{$value}%"]
+                ]
+            );
+        }
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/etc/di.xml b/app/code/Coditron/CustomShippingRate/etc/di.xml
index 68c6a903c..c5223cb16 100644
--- a/app/code/Coditron/CustomShippingRate/etc/di.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/di.xml
@@ -6,6 +6,9 @@
 	<type name="Magento\Checkout\Model\ShippingInformationManagement">
 		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
 	</type>
+	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
+		<plugin name="enable_freeshipping_with_thresholds" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
+	</type>
 	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
 	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                 type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
new file mode 100644
index *********..761c13ee4
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0"?>
+
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <head>
+
+        <css src="Webkul_Marketplace::css/wk_block.css"/>
+        <css src="Webkul_Marketplace::css/style.css"/>
+        <css src="Webkul_Marketplace::css/product.css"/>
+        <css src="Webkul_Marketplace::css/layout.css"/>
+        <css src="Coditron_CustomShippingRate::css/select2/select2.css"/>
+    </head>
+    <body>
+        <referenceContainer name="seller.content">
+            <block class="Coditron\CustomShippingRate\Block\TableRates" name="mpsellership_threshold_edit" template="Coditron_CustomShippingRate::shiprate/edit_threshold.phtml" cacheable="false"></block>
+        </referenceContainer>
+    </body>
+</page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..73e7e885b 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -20,6 +20,7 @@
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..a6b06a5e1 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = $block->isenable();
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
new file mode 100644
index *********..23aa70654
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
@@ -0,0 +1,114 @@
+<?php
+/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
+$helper = $block->getMpHelper();
+$isPartner = $helper->isSeller();
+$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';
+
+$sellerId = $block->getSellerId();
+$shipRate = $block->getShipRate();
+
+$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
+?>
+<div class="wk-mpsellercategory-container">
+    <?php if ($isPartner == 1): ?>
+        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
+        enctype="multipart/form-data" method="post" id="form-save-threshold"
+        data-mage-init='{"validation":{}}'>
+            <div class="fieldset wk-ui-component-container">
+                <?= $block->getBlockHtml('formkey') ?>
+                <?= $block->getBlockHtml('seller.formkey') ?>
+                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
+                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
+                <input type="hidden" name="is_threshold" value="1">
+                
+                <!-- Set default values for threshold mode -->
+                <input type="hidden" name="courier_name" value="<?= $escaper->escapeHtml($shipRate->getCourierName() ?: 'Free Shipping') ?>">
+                <input type="hidden" name="service_type" value="<?= $escaper->escapeHtml($shipRate->getServiceType() ?: 'free_shipping') ?>">
+                <input type="hidden" name="weight" value="<?= $escaper->escapeHtml($shipRate->getWeight() ?: '999999') ?>">
+                <input type="hidden" name="shipping_price" value="<?= $escaper->escapeHtml($shipRate->getShippingPrice() ?: '0') ?>">
+                <input type="hidden" name="free_shipping" value="1">
+                <input type="hidden" name="packing_time" value="<?= $escaper->escapeHtml($shipRate->getPackingTime() ?: '0') ?>">
+                <input type="hidden" name="delivery_time" value="<?= $escaper->escapeHtml($shipRate->getDeliveryTime() ?: '0') ?>">
+                <input type="hidden" name="total_lead_time" value="<?= $escaper->escapeHtml($shipRate->getTotalLeadTime() ?: '0') ?>">
+                <input type="hidden" name="return_address_id" value="<?= $escaper->escapeHtml($shipRate->getReturnAddressId() ?: '0') ?>">
+                
+                <div class="page-main-actions">
+                    <div class="page-actions-placeholder"></div>
+                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Free Shipping Threshold")); ?>">
+                            <div class="page-actions-buttons">
+                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
+                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
+                                data-ui-id="back-button">
+                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
+                                </button>
+                                <button id="save"
+                                title="<?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?>" type="submit"
+                                class="action- scalable save primary ui-button ui-widget
+                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
+                                wk-ui-grid-btn-primary"
+                                data-form-role="save"
+                                data-ui-id="save-button" role="button" aria-disabled="false">
+                                    <span class="ui-button-text">
+                                        <span><?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?></span>
+                                    </span>
+                                </button>
+                            </div>
+                        </div>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label class="label" for="countries">
+                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
+                    </label>
+                    <div class="tooltip">
+                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where free shipping applies')) ?></span>
+                    </div>
+                    <div class="control">
+                        <?php echo $countriesListHtml; ?>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label for="min_order_amount" class="label">
+                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
+                    </label>
+                    <div class="tooltip">
+                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
+                     </div>
+                    <div class="control">
+                        <input type="text" class="input-text required-entry validate-number validate-zero-or-greater" 
+                        name="min_order_amount"
+                        data-validate="{required:true, 'validate-number':true, 'validate-zero-or-greater':true}" 
+                        title="<?= $escaper->escapeHtml(__("Minimum Order Amount")); ?>"
+                        id="min_order_amount" 
+                        value="<?= $escaper->escapeHtml($shipRate->getMinOrderAmount()) ?>">
+                    </div>
+                </div>
+            </div>
+        </form>
+    <?php else: ?>
+        <h2 class="wk-mp-error-msg">
+            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
+        </h2>
+    <?php endif; ?>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#form-save-threshold": {
+        "Coditron_CustomShippingRate/js/threshold-form": {
+            "backUrl": "<?= $escaper->escapeJs($backUrl) ?>"
+        }
+    }
+}
+</script>
+
+<style>
+    .select2-search__field {
+        height: auto !important;
+    }
+</style>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
index 15601be5f..351fae526 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
@@ -10,21 +10,51 @@
  */
 ?>
 <div class="wk-mpsellercategory-container">
-    <div class="page-main-actions">
-        <div class="page-actions-placeholder"></div>
-        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
-            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
-                <div class="page-actions-buttons">
-                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
-                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
-                    onclick="location.href
-                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
-                    data-ui-id="add-button">
-                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
-                    </button>
+    <!-- Shipping Methods Section -->
+    <div class="wk-mp-section">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Shipping Methods')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
+                        data-ui-id="add-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
+                        </button>
+                    </div>
                 </div>
             </div>
         </div>
+        <div id="shipping-methods-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
+        </div>
+    </div>
+
+    <!-- Free Shipping Thresholds Section -->
+    <div class="wk-mp-section" style="margin-top: 40px;">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['is_threshold' => 1]))?>';"
+                        data-ui-id="add-threshold-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
+                        </button>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div id="threshold-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
+        </div>
     </div>
-    <?= /* @noEscape */ $block->getChildHtml(); ?>
 </div>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
new file mode 100644
index *********..7983fc7c5
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
@@ -0,0 +1,117 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<!--
+/**
+ * Free Shipping Threshold UI Component for Seller Dashboard
+ */
+-->
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <argument name="data" xsi:type="array">
+        <item name="js_config" xsi:type="array">
+            <item name="provider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+            <item name="deps" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+        </item>
+        <item name="spinner" xsi:type="string">sellership_threshold_columns</item>
+    </argument>
+    <dataSource name="sellership_threshold_list_front_data_source">
+        <argument name="dataProvider" xsi:type="configurableObject">
+            <argument name="class" xsi:type="string">Coditron\CustomShippingRate\Ui\DataProvider\ThresholdListDataProvider</argument>
+            <argument name="name" xsi:type="string">sellership_threshold_list_front_data_source</argument>
+            <argument name="primaryFieldName" xsi:type="string">shiptablerates_id</argument>
+            <argument name="requestFieldName" xsi:type="string">id</argument>
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
+                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
+                    <item name="storageConfig" xsi:type="array">
+                        <item name="cacheRequests" xsi:type="boolean">false</item>
+                    </item>
+                </item>
+            </argument>
+        </argument>
+    </dataSource>
+    <listingToolbar name="listing_top">
+        <columnsControls name="columns_controls"/>
+        <filters name="listing_filters">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="statefull" xsi:type="array">
+                        <item name="applied" xsi:type="boolean">false</item>
+                    </item>
+                    <item name="params" xsi:type="array">
+                        <item name="filters_modifier" xsi:type="array" />
+                    </item>
+                </item>
+            </argument>
+        </filters>
+        <massaction name="listing_massaction">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="selectProvider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front.sellership_threshold_columns.ids</item>
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+            <action name="delete">
+                <argument name="data" xsi:type="array">
+                    <item name="config" xsi:type="array">
+                        <item name="type" xsi:type="string">delete</item>
+                        <item name="label" xsi:type="string" translate="true">Delete</item>
+                        <item name="url" xsi:type="url" path="coditron_customshippingrate/shiptablerates/delete"/>
+                        <item name="confirm" xsi:type="array">
+                            <item name="title" xsi:type="string" translate="true">Delete Threshold</item>
+                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected free shipping thresholds?</item>
+                        </item>
+                    </item>
+                </argument>
+            </action>
+        </massaction>
+        <paging name="listing_paging"/>
+    </listingToolbar>
+    <columns name="sellership_threshold_columns">
+        <argument name="data" xsi:type="array">
+            <item name="config" xsi:type="array">
+                <item name="childDefaults" xsi:type="array">
+                    <item name="fieldAction" xsi:type="array">
+                        <item name="provider" xsi:type="string">thresholdGrid</item>
+                        <item name="target" xsi:type="string">selectThreshold</item>
+                        <item name="params" xsi:type="array">
+                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
+                        </item>
+                    </item>
+                </item>
+            </item>
+        </argument>
+        <selectionsColumn name="ids">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+        </selectionsColumn>
+        <column name="countries">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Countries</item>
+                    <item name="sortOrder" xsi:type="number">20</item>
+                </item>
+            </argument>
+        </column>
+        <column name="min_order_amount">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Minimum Order Amount (USD)</item>
+                    <item name="sortOrder" xsi:type="number">30</item>
+                </item>
+            </argument>
+        </column>
+        <actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ThresholdActions">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                    <item name="sortOrder" xsi:type="number">100</item>
+                </item>
+            </argument>
+        </actionsColumn>
+    </columns>
+</listing>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
new file mode 100644
index *********..36ed42380
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
@@ -0,0 +1,16 @@
+define(['jquery', 'select2'], function($) {
+    'use strict';
+
+    return function(config, element) {
+        $(document).ready(function() {
+            $("#back").click(function(){
+                window.location.replace(config.backUrl);
+            });
+
+            $('.custom-multiselect').select2({
+                placeholder: "Select countries",
+                allowClear: true
+            });
+        });
+    };
+});
